{"name": "my-react-router-app", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@epic-web/remember": "^1.1.0", "@react-router/node": "^7.5.3", "@react-router/serve": "^7.5.3", "@tanstack/react-query": "^5.80.6", "@tanstack/react-query-devtools": "^5.80.6", "isbot": "^5.1.27", "match-sorter": "^8.0.2", "msw": "^2.10.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.5.3", "sort-by": "^1.2.0", "tiny-invariant": "^1.3.3"}, "devDependencies": {"@react-router/dev": "^7.5.3", "@tailwindcss/vite": "^4.1.4", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "tailwindcss": "^4.1.4", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4"}, "msw": {"workerDirectory": ["public"]}}