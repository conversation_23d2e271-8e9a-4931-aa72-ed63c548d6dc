import {
  Outlet,
  <PERSON><PERSON><PERSON>,
  ScrollRestoration,
  isRouteErrorResponse,
  redirect,
} from "react-router";
import type { Route } from "./+types/root";

import appStylesHref from "./app.css?url";
import { createEmptyContact } from "./data";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "./lib/query-client";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { ToastProvider } from "./components/toast";
import { classifyError, ErrorType } from "./lib/errors";

export async function clientAction() {
  const contact = await createEmptyContact();

  queryClient.invalidateQueries({ queryKey: ["contacts"] });

  return redirect(`/contacts/${contact.id}/edit`);
}

export default function App() {
  return <Outlet />;
}

export function HydrateFallback() {
  return (
    <div id="loading-splash">
      <div id="loading-splash-spinner" />
      <p>Loading, please wait...</p>
    </div>
  );
}

// The Layout component is a special export for the root route.
// It acts as your document's "app shell" for all route components, HydrateFallback, and ErrorBoundary
// For more information, see https://reactrouter.com/explanation/special-files#layout-export
export function Layout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="stylesheet" href={appStylesHref} />
      </head>
      <body>
        <QueryClientProvider client={queryClient}>
          <ToastProvider>
            {children}
            <ReactQueryDevtools initialIsOpen={false} />
          </ToastProvider>
        </QueryClientProvider>
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}

// The top most error boundary for the app, rendered when your app throws an error
// For more information, see https://reactrouter.com/start/framework/route-module#errorboundary
export function ErrorBoundary({ error }: Route.ErrorBoundaryProps) {
  const appError = classifyError(error);

  let message = "Oops!";
  let details = appError.userMessage;
  let stack: string | undefined;
  let canRetry = appError.isRetryable;
  let showReload = true;

  if (isRouteErrorResponse(error)) {
    message = error.status === 404 ? "404" : "Error";
    details =
      error.status === 404
        ? "The requested page could not be found."
        : error.statusText || details;
    canRetry = error.status >= 500; // Server errors are retryable
    showReload = error.status !== 404; // Don't show reload for 404s
  } else if (import.meta.env.DEV && error && error instanceof Error) {
    details = error.message;
    stack = error.stack;
  }

  const handleRetry = () => {
    window.location.reload();
  };

  const handleGoHome = () => {
    window.location.href = "/";
  };

  return (
    <main id="error-page">
      <div className="error-content">
        <h1>{message}</h1>
        <p className="error-message">{details}</p>

        {appError.type === ErrorType.NETWORK && (
          <div className="error-help">
            <p>This might be due to:</p>
            <ul>
              <li>Poor internet connection</li>
              <li>Server maintenance</li>
              <li>Temporary service disruption</li>
            </ul>
          </div>
        )}

        <div className="error-actions">
          {canRetry && showReload && (
            <button onClick={handleRetry} className="error-button primary">
              Try Again
            </button>
          )}
          <button onClick={handleGoHome} className="error-button secondary">
            Go Home
          </button>
        </div>

        {stack && import.meta.env.DEV && (
          <details className="error-debug">
            <summary>Debug Information (Development Only)</summary>
            <div className="error-details">
              <h3>Error Details:</h3>
              <p>
                <strong>Type:</strong> {appError.type}
              </p>
              <p>
                <strong>Severity:</strong> {appError.severity}
              </p>
              <p>
                <strong>Retryable:</strong>{" "}
                {appError.isRetryable ? "Yes" : "No"}
              </p>
              <p>
                <strong>Timestamp:</strong> {appError.context.timestamp}
              </p>

              <h3>Stack Trace:</h3>
              <pre>
                <code>{stack}</code>
              </pre>

              <h3>Context:</h3>
              <pre>
                <code>{JSON.stringify(appError.context, null, 2)}</code>
              </pre>
            </div>
          </details>
        )}
      </div>
    </main>
  );
}
