import { Form, redirect, useNavigate } from "react-router";

import { getContact, updateContact } from "../data";
import type { Route } from "./+types/edit-contact";
import {
  queryClient,
  handleQueryError,
  handleMutationError,
} from "../lib/query-client";
import { SectionErrorBoundary } from "../components/error-boundary";
import {
  contactValidationSchema,
  handleFormSubmission,
} from "../lib/validation";

export async function clientLoader({ params }: Route.LoaderArgs) {
  try {
    const contact = await queryClient.fetchQuery({
      queryKey: ["contact-details", params.contactId],
      queryFn: () => getContact(params.contactId),
    });

    return { contact };
  } catch (error) {
    throw handleQueryError(error, ["contact-details", params.contactId]);
  }
}

export async function clientAction({ params, request }: Route.ActionArgs) {
  try {
    const formData = await request.formData();

    // Use enhanced form submission with validation
    await handleFormSubmission(formData, contactValidationSchema, (data) =>
      updateContact(params.contactId, data)
    );

    queryClient.invalidateQueries({ queryKey: ["contacts"] });
    queryClient.invalidateQueries({ queryKey: ["contact-details"] });

    return redirect(`/contacts/${params.contactId}`);
  } catch (error) {
    throw handleMutationError(error, ["update-contact", params.contactId]);
  }
}

export default function EditContact({ loaderData }: Route.ComponentProps) {
  const { contact } = loaderData;
  const navigate = useNavigate();

  return (
    <SectionErrorBoundary title="Edit Contact">
      <Form key={contact.id} id="contact-form" method="post">
        <p>
          <span>Name</span>
          <input
            aria-label="First name"
            defaultValue={contact.first}
            name="first"
            placeholder="First"
            type="text"
            required
          />
          <input
            aria-label="Last name"
            defaultValue={contact.last}
            name="last"
            placeholder="Last"
            type="text"
            required
          />
        </p>
        <label>
          <span>Twitter</span>
          <input
            defaultValue={contact.twitter}
            name="twitter"
            placeholder="@jack"
            type="text"
            pattern="^@?[a-zA-Z0-9_]+$"
            title="Twitter handle can only contain letters, numbers, and underscores"
          />
        </label>
        <label>
          <span>Avatar URL</span>
          <input
            aria-label="Avatar URL"
            defaultValue={contact.avatar}
            name="avatar"
            placeholder="https://example.com/avatar.jpg"
            type="url"
          />
        </label>
        <label>
          <span>Notes</span>
          <textarea defaultValue={contact.notes} name="notes" rows={6} />
        </label>
        <p>
          <button type="submit">Save</button>
          <button onClick={() => navigate(-1)} type="button">
            Cancel
          </button>
        </p>
      </Form>
    </SectionErrorBoundary>
  );
}
