import { Form, useFetcher } from "react-router";

import { getContact, updateContact, type ContactRecord } from "../data";
import type { Route } from "./+types/contact";
import {
  queryClient,
  handleQueryError,
  handleMutationError,
} from "../lib/query-client";
import {
  SectionErrorBoundary,
  ComponentErrorBoundary,
} from "../components/error-boundary";

export async function clientLoader({ params }: Route.LoaderArgs) {
  try {
    const contact = await queryClient.fetchQuery({
      queryKey: ["contact-details", params.contactId],
      queryFn: () => getContact(params.contactId),
    });

    return { contact };
  } catch (error) {
    throw handleQueryError(error, ["contact-details", params.contactId]);
  }
}

export async function clientAction({ params, request }: Route.ActionArgs) {
  try {
    const formData = await request.formData();

    await updateContact(params.contactId, {
      favorite: formData.get("favorite") === "true",
    });

    queryClient.invalidateQueries({ queryKey: ["contacts"] });
    queryClient.invalidateQueries({ queryKey: ["contact-details"] });
  } catch (error) {
    throw handleMutationError(error, ["update-favorite", params.contactId]);
  }
}

export default function Contact({ loaderData }: Route.ComponentProps) {
  const { contact } = loaderData;

  return (
    <SectionErrorBoundary title="Contact Details">
      <div id="contact">
        <div>
          <ComponentErrorBoundary>
            <img
              alt={`${contact.first} ${contact.last} avatar`}
              key={contact.avatar}
              src={contact.avatar}
              onError={(e) => {
                // Fallback for broken images
                e.currentTarget.src =
                  "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMDAgMTAwQzExMC40NTcgMTAwIDExOSA5MS40NTY2IDExOSA4MUM4MSA3MC41NDM0IDEwMCA2MCA4MSA2MEM3MC41NDM0IDYwIDYyIDY4LjU0MzQgNjIgNzlDNjIgODkuNDU2NiA3MC41NDM0IDk4IDgxIDk4QzkxLjQ1NjYgOTggMTAwIDg5LjQ1NjYgMTAwIDc5WiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMTAwIDEyMEMxMjIuMDkxIDEyMCAxNDAgMTM3LjkwOSAxNDAgMTYwSDE2MFYxODBIMjBWMTYwSDQwQzQwIDEzNy45MDkgNTcuOTA4NiAxMjAgODAgMTIwSDEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+";
              }}
            />
          </ComponentErrorBoundary>
        </div>

        <div>
          <h1>
            {contact.first || contact.last ? (
              <>
                {contact.first} {contact.last}
              </>
            ) : (
              <i>No Name</i>
            )}
            <ComponentErrorBoundary>
              <Favorite contact={contact} />
            </ComponentErrorBoundary>
          </h1>

          {contact.twitter ? (
            <p>
              <a
                href={`https://twitter.com/${contact.twitter}`}
                target="_blank"
                rel="noopener noreferrer"
              >
                {contact.twitter}
              </a>
            </p>
          ) : null}

          {contact.notes ? <p>{contact.notes}</p> : null}

          <div>
            <Form action="edit">
              <button type="submit">Edit</button>
            </Form>

            <Form
              action="destroy"
              method="post"
              onSubmit={(event) => {
                const response = confirm(
                  "Please confirm you want to delete this record."
                );
                if (!response) {
                  event.preventDefault();
                }
              }}
            >
              <button type="submit">Delete</button>
            </Form>
          </div>
        </div>
      </div>
    </SectionErrorBoundary>
  );
}

function Favorite({ contact }: { contact: Pick<ContactRecord, "favorite"> }) {
  const fetcher = useFetcher();

  const favorite = fetcher.formData
    ? fetcher.formData.get("favorite")
    : contact.favorite;

  return (
    <fetcher.Form method="post">
      <button
        aria-label={favorite ? "Remove from favorites" : "Add to favorites"}
        name="favorite"
        value={favorite ? "false" : "true"}
      >
        {favorite ? "★" : "☆"}
      </button>
    </fetcher.Form>
  );
}
