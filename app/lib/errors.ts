// Error types and utilities for enhanced error handling

export enum ErrorType {
  NETWORK = 'NETWORK',
  VALIDATION = 'VALIDATION',
  NOT_FOUND = 'NOT_FOUND',
  UNAUTHORIZED = 'UNAUTHORIZED',
  SERVER = 'SERVER',
  CLIENT = 'CLIENT',
  UNKNOWN = 'UNKNOWN'
}

export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export interface ErrorContext {
  userId?: string;
  route?: string;
  action?: string;
  timestamp: string;
  userAgent?: string;
  additionalData?: Record<string, any>;
}

export class AppError extends Error {
  public readonly type: ErrorType;
  public readonly severity: ErrorSeverity;
  public readonly context: ErrorContext;
  public readonly isRetryable: boolean;
  public readonly userMessage: string;

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context: Partial<ErrorContext> = {},
    isRetryable: boolean = false,
    userMessage?: string
  ) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.severity = severity;
    this.isRetryable = isRetryable;
    this.userMessage = userMessage || this.getDefaultUserMessage();
    this.context = {
      timestamp: new Date().toISOString(),
      route: typeof window !== 'undefined' ? window?.location?.pathname : undefined,
      userAgent: typeof navigator !== 'undefined' ? navigator?.userAgent : undefined,
      ...context
    };
  }

  private getDefaultUserMessage(): string {
    switch (this.type) {
      case ErrorType.NETWORK:
        return 'Network connection issue. Please check your internet connection and try again.';
      case ErrorType.NOT_FOUND:
        return 'The requested resource could not be found.';
      case ErrorType.UNAUTHORIZED:
        return 'You are not authorized to perform this action.';
      case ErrorType.VALIDATION:
        return 'Please check your input and try again.';
      case ErrorType.SERVER:
        return 'Server error occurred. Please try again later.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }
}

export class NetworkError extends AppError {
  constructor(message: string, context?: Partial<ErrorContext>) {
    super(
      message,
      ErrorType.NETWORK,
      ErrorSeverity.MEDIUM,
      context,
      true,
      'Network connection issue. Please check your internet connection and try again.'
    );
  }
}

export class ValidationError extends AppError {
  public readonly field?: string;
  public readonly validationRule?: string;

  constructor(
    message: string,
    field?: string,
    validationRule?: string,
    context?: Partial<ErrorContext>
  ) {
    super(
      message,
      ErrorType.VALIDATION,
      ErrorSeverity.LOW,
      context,
      false,
      'Please check your input and try again.'
    );
    this.field = field;
    this.validationRule = validationRule;
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string, context?: Partial<ErrorContext>) {
    super(
      `${resource} not found`,
      ErrorType.NOT_FOUND,
      ErrorSeverity.MEDIUM,
      context,
      false,
      `The requested ${resource.toLowerCase()} could not be found.`
    );
  }
}

// Error classification utilities
export function classifyError(error: unknown): AppError {
  if (error instanceof AppError) {
    return error;
  }

  if (error instanceof Response) {
    const status = error.status;
    if (status === 404) {
      return new NotFoundError('Resource', { additionalData: { status } });
    }
    if (status === 401 || status === 403) {
      return new AppError(
        'Unauthorized access',
        ErrorType.UNAUTHORIZED,
        ErrorSeverity.MEDIUM,
        { additionalData: { status } }
      );
    }
    if (status >= 500) {
      return new AppError(
        'Server error',
        ErrorType.SERVER,
        ErrorSeverity.HIGH,
        { additionalData: { status } },
        true
      );
    }
  }

  if (error instanceof TypeError && error.message.includes('fetch')) {
    return new NetworkError('Network request failed');
  }

  if (error instanceof Error) {
    return new AppError(
      error.message,
      ErrorType.CLIENT,
      ErrorSeverity.MEDIUM,
      { additionalData: { originalError: error.name } }
    );
  }

  return new AppError(
    'Unknown error occurred',
    ErrorType.UNKNOWN,
    ErrorSeverity.MEDIUM,
    { additionalData: { error: String(error) } }
  );
}

// Error reporting
export interface ErrorReporter {
  report(error: AppError): void;
}

export class ConsoleErrorReporter implements ErrorReporter {
  report(error: AppError): void {
    const logLevel = this.getLogLevel(error.severity);
    console[logLevel]('Error Report:', {
      message: error.message,
      type: error.type,
      severity: error.severity,
      context: error.context,
      stack: error.stack
    });
  }

  private getLogLevel(severity: ErrorSeverity): 'error' | 'warn' | 'info' {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        return 'error';
      case ErrorSeverity.MEDIUM:
        return 'warn';
      default:
        return 'info';
    }
  }
}

// Global error reporter instance
export const errorReporter = new ConsoleErrorReporter();

// Error handling utilities
export function handleError(error: unknown, context?: Partial<ErrorContext>): AppError {
  const appError = classifyError(error);
  
  // Add additional context
  if (context) {
    appError.context = { ...appError.context, ...context };
  }
  
  // Report the error
  errorReporter.report(appError);
  
  return appError;
}

export function isRetryableError(error: unknown): boolean {
  if (error instanceof AppError) {
    return error.isRetryable;
  }
  
  // Network errors are generally retryable
  if (error instanceof TypeError && error.message.includes('fetch')) {
    return true;
  }
  
  // 5xx server errors are retryable
  if (error instanceof Response && error.status >= 500) {
    return true;
  }
  
  return false;
}
