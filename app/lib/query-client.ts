import { QueryClient } from "@tanstack/react-query";
import { remember } from "@epic-web/remember";
import { handleError, isRetryableError, NetworkError } from "./errors";

export const queryClient = remember(
  "react-query",
  () =>
    new QueryClient({
      defaultOptions: {
        queries: {
          staleTime: 1000 * 10,
          retry: (failureCount, error) => {
            // Don't retry on 4xx errors (client errors)
            if (
              error instanceof Response &&
              error.status >= 400 &&
              error.status < 500
            ) {
              return false;
            }

            // Retry up to 3 times for retryable errors
            if (isRetryableError(error) && failureCount < 3) {
              return true;
            }

            return false;
          },
          retryDelay: (attemptIndex) =>
            Math.min(1000 * 2 ** attemptIndex, 30000),
        },
        mutations: {
          retry: (failureCount, error) => {
            // Generally don't retry mutations unless it's a network error
            if (error instanceof NetworkError && failureCount < 2) {
              return true;
            }
            return false;
          },
        },
      },
    })
);

// Utility functions for handling React Query errors
export function handleQueryError(error: unknown, queryKey?: unknown[]) {
  return handleError(error, {
    action: "query_error",
    additionalData: { queryKey },
  });
}

export function handleMutationError(error: unknown, mutationKey?: unknown[]) {
  return handleError(error, {
    action: "mutation_error",
    additionalData: { mutationKey },
  });
}
