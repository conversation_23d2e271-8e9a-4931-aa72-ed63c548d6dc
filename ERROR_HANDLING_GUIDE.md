# Enhanced Error Handling System

This document describes the comprehensive error handling system implemented in the React Router application.

## Overview

The error handling system provides multiple layers of protection and user-friendly error experiences:

1. **Custom Error Types** - Structured error classification
2. **Error Boundaries** - Component-level error recovery
3. **Toast Notifications** - User-friendly error messages
4. **Form Validation** - Input validation and error display
5. **React Query Integration** - Enhanced async error handling
6. **Retry Mechanisms** - Automatic and manual error recovery

## Components

### 1. Error Types (`app/lib/errors.ts`)

#### Custom Error Classes
- `AppError` - Base error class with context and user messages
- `NetworkError` - Network-related errors (retryable)
- `ValidationError` - Form validation errors
- `NotFoundError` - Resource not found errors

#### Error Classification
```typescript
export enum ErrorType {
  NETWORK = 'NETWORK',
  VALIDATION = 'VALIDATION',
  NOT_FOUND = 'NOT_FOUND',
  UNAUTHORIZED = 'UNAUTHORIZED',
  SERVER = 'SERVER',
  CLIENT = 'CLIENT',
  UNKNOWN = 'UNKNOWN'
}

export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}
```

#### Usage Example
```typescript
import { handleError, NetworkError } from './lib/errors';

try {
  await fetchData();
} catch (error) {
  throw handleError(error, { action: 'fetch_data' });
}
```

### 2. Error Boundaries (`app/components/error-boundary.tsx`)

#### Available Boundaries
- `ErrorBoundary` - Generic error boundary with retry logic
- `PageErrorBoundary` - Page-level error handling
- `SectionErrorBoundary` - Section-level error handling
- `ComponentErrorBoundary` - Component-level error handling

#### Features
- Automatic retry for retryable errors
- Exponential backoff for auto-retry
- Contextual error messages
- Development debug information

#### Usage Example
```tsx
import { SectionErrorBoundary } from '../components/error-boundary';

function MyComponent() {
  return (
    <SectionErrorBoundary title="User Profile">
      <UserProfile />
    </SectionErrorBoundary>
  );
}
```

### 3. Toast Notifications (`app/components/toast.tsx`)

#### Features
- Auto-dismissing notifications
- Action buttons for retryable errors
- Different types: success, error, warning, info
- Accessible with ARIA labels

#### Usage Example
```tsx
import { useToast, useErrorToast } from '../components/toast';

function MyComponent() {
  const { showSuccess, showError } = useToast();
  const showErrorToast = useErrorToast();

  const handleSuccess = () => {
    showSuccess('Operation completed successfully!');
  };

  const handleError = (error: unknown) => {
    showErrorToast(error);
  };
}
```

### 4. Form Validation (`app/lib/validation.ts`)

#### Validation Rules
- `required` - Required field validation
- `minLength` / `maxLength` - String length validation
- `email` - Email format validation
- `url` - URL format validation
- `pattern` - Custom regex validation
- `custom` - Custom validation functions

#### Usage Example
```tsx
import { useFormValidation, contactValidationSchema } from '../lib/validation';

function ContactForm() {
  const {
    errors,
    validateField,
    validateForm,
    getFieldError,
    hasFieldError
  } = useFormValidation(contactValidationSchema);

  const handleSubmit = (formData: FormData) => {
    const data = Object.fromEntries(formData);
    const result = validateForm(data);
    
    if (!result.isValid) {
      // Handle validation errors
      return;
    }
    
    // Submit form
  };
}
```

### 5. React Query Integration (`app/lib/query-client.ts`)

#### Enhanced Features
- Automatic retry for retryable errors
- Exponential backoff retry delay
- Error classification and reporting
- Custom error handling utilities

#### Configuration
```typescript
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error) => {
        // Don't retry on 4xx errors
        if (error instanceof Response && error.status >= 400 && error.status < 500) {
          return false;
        }
        // Retry up to 3 times for retryable errors
        return isRetryableError(error) && failureCount < 3;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
  },
});
```

## Implementation Examples

### Route Error Handling

```tsx
// In route loaders
export async function clientLoader({ params }: Route.LoaderArgs) {
  try {
    const contact = await queryClient.fetchQuery({
      queryKey: ["contact-details", params.contactId],
      queryFn: () => getContact(params.contactId),
    });
    return { contact };
  } catch (error) {
    throw handleQueryError(error, ["contact-details", params.contactId]);
  }
}

// In route actions
export async function clientAction({ params, request }: Route.ActionArgs) {
  try {
    const formData = await request.formData();
    await handleFormSubmission(
      formData,
      contactValidationSchema,
      (data) => updateContact(params.contactId, data)
    );
    return redirect(`/contacts/${params.contactId}`);
  } catch (error) {
    throw handleMutationError(error, ["update-contact", params.contactId]);
  }
}
```

### Component Error Handling

```tsx
export default function ContactDetails({ loaderData }: Route.ComponentProps) {
  const { contact } = loaderData;

  return (
    <SectionErrorBoundary title="Contact Details">
      <div>
        <ComponentErrorBoundary>
          <img 
            src={contact.avatar} 
            onError={(e) => {
              e.currentTarget.src = '/fallback-avatar.svg';
            }}
          />
        </ComponentErrorBoundary>
        
        <ComponentErrorBoundary>
          <FavoriteButton contact={contact} />
        </ComponentErrorBoundary>
      </div>
    </SectionErrorBoundary>
  );
}
```

## Error Recovery Strategies

### 1. Automatic Retry
- Network errors are automatically retried with exponential backoff
- Server errors (5xx) are retried up to 3 times
- Client errors (4xx) are not retried

### 2. Manual Retry
- Error boundaries provide retry buttons for retryable errors
- Toast notifications include retry actions
- Users can manually refresh or retry operations

### 3. Graceful Degradation
- Broken images fall back to placeholder
- Failed components show error messages instead of crashing
- Network failures show helpful troubleshooting tips

### 4. User Feedback
- Clear, actionable error messages
- Loading states during retry attempts
- Success notifications for completed operations

## Best Practices

### 1. Error Classification
- Always classify errors using the `handleError` utility
- Provide context information for debugging
- Use appropriate error types and severity levels

### 2. User Experience
- Show user-friendly messages, not technical details
- Provide clear next steps or recovery options
- Use toast notifications for non-critical errors

### 3. Error Boundaries
- Use granular error boundaries to isolate failures
- Provide fallback UI that maintains app functionality
- Include retry mechanisms for recoverable errors

### 4. Form Validation
- Validate on both client and server
- Show field-level errors immediately
- Provide clear validation rules and messages

### 5. Monitoring
- Log errors with sufficient context for debugging
- Track error rates and patterns
- Monitor retry success rates

## Styling

The error handling components include comprehensive CSS styling:

- Error pages with helpful information
- Toast notifications with animations
- Error boundary fallbacks with retry buttons
- Form validation error displays
- Loading states and spinners

All styles are included in `app/app.css` and follow the existing design system.
